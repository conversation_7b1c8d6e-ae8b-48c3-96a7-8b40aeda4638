#!/bin/bash

# Step 1: Environment Setup
echo "Setting up environment..."
sudo apt-get update
sudo apt-get install -y go nodejs npm

# Step 2: Configuration
echo "Setting up configuration..."
cp configs/email-intelligence.yaml configs/email-intelligence.yaml.bak
sed -i 's/your_api_key/your_actual_api_key/g' configs/email-intelligence.yaml

# Step 3: Database Initialization
echo "Initializing database..."
go run cmd/unified-hvac-crm/main.go migrate up

# Step 4: Service Initialization
echo "Starting services..."
go run cmd/unified-hvac-crm/main.go &

# Step 5: Testing
echo "Running tests..."
go test ./internal/offer -v

echo "Setup complete!"
