package offer

import (
	"context"
	"testing"
	"time"

	"github.com/tmc/langchaingo/llms"
)

// TestHVACOfferGenerator - Test podstawowej funkcjonalności generatora ofert
func TestHVACOfferGenerator(t *testing.T) {
	// Pomiń test jeśli nie ma klucza API
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Inicjalizacja generatora
	generator, err := NewHVACOfferGenerator()
	if err != nil {
		t.Fatalf("Failed to create generator: %v", err)
	}

	// Test generowania oferty
	ctx := context.Background()
	customerID := "test_customer_123"

	offer, err := generator.GenerateOffer(ctx, customerID)
	if err != nil {
		t.Fatalf("Failed to generate offer: %v", err)
	}

	// Sprawdź podstawowe pola
	if offer.OfferID == "" {
		t.Error("Offer ID should not be empty")
	}

	if offer.CustomerProfile.CustomerID != customerID {
		t.Errorf("Expected customer ID %s, got %s", customerID, offer.CustomerProfile.CustomerID)
	}

	if len(offer.Recommendations) == 0 {
		t.Error("Should have at least one recommendation")
	}

	if offer.PersonalizedIntro == "" {
		t.Error("Personalized intro should not be empty")
	}

	if offer.PDFPath == "" {
		t.Error("PDF path should not be empty")
	}

	t.Logf("✅ Offer generated successfully: %s", offer.OfferID)
	t.Logf("📄 PDF path: %s", offer.PDFPath)
	t.Logf("💰 Final price: %.2f PLN", offer.Summary.FinalPrice)
}

// TestLGEquipmentDatabase - Test bazy danych urządzeń LG
func TestLGEquipmentDatabase(t *testing.T) {
	db := NewLGEquipmentDatabase()

	// Test pobierania urządzenia
	equipment, exists := db.GetEquipmentByModel("S12ET")
	if !exists {
		t.Error("S12ET equipment should exist")
	}

	if equipment.Name != "LG Standard S12ET" {
		t.Errorf("Expected name 'LG Standard S12ET', got '%s'", equipment.Name)
	}

	// Test wszystkich urządzeń
	allEquipment := db.GetAllEquipment()
	if len(allEquipment) < 3 {
		t.Error("Should have at least 3 equipment models")
	}

	t.Logf("✅ Equipment database test passed, found %d models", len(allEquipment))
}

// TestCustomerSemanticAnalyzer - Test analizatora semantycznego
func TestCustomerSemanticAnalyzer(t *testing.T) {
	// Mock LLM dla testów
	mockLLM := &MockLLM{}
	analyzer := NewCustomerSemanticAnalyzer(mockLLM)

	ctx := context.Background()
	customerID := "test_customer_123"

	profile, err := analyzer.AnalyzeCustomer(ctx, customerID)
	if err != nil {
		t.Fatalf("Failed to analyze customer: %v", err)
	}

	if profile.CustomerID != customerID {
		t.Errorf("Expected customer ID %s, got %s", customerID, profile.CustomerID)
	}

	if profile.Requirements.RoomSize <= 0 {
		t.Error("Room size should be greater than 0")
	}

	t.Logf("✅ Customer analysis test passed")
	t.Logf("🏠 Room size: %.0f m²", profile.Requirements.RoomSize)
	t.Logf("💰 Budget: %.0f - %.0f PLN", profile.Budget.MinPrice, profile.Budget.MaxPrice)
}

// MockLLM - Mock implementacja LLM dla testów
type MockLLM struct{}

func (m *MockLLM) Call(ctx context.Context, prompt string, options ...llms.CallOption) (string, error) {
	return m.GenerateFromSinglePrompt(ctx, prompt)
}

func (m *MockLLM) GenerateContent(ctx context.Context, messages []llms.MessageContent, options ...llms.CallOption) (*llms.ContentResponse, error) {
	// Wyciągnij prompt z pierwszej wiadomości
	prompt := ""
	if len(messages) > 0 && len(messages[0].Parts) > 0 {
		if textPart, ok := messages[0].Parts[0].(llms.TextContent); ok {
			prompt = textPart.Text
		}
	}

	content, err := m.GenerateFromSinglePrompt(ctx, prompt)
	if err != nil {
		return nil, err
	}

	return &llms.ContentResponse{
		Choices: []*llms.ContentChoice{
			{
				Content: content,
			},
		},
	}, nil
}

func (m *MockLLM) GenerateFromSinglePrompt(ctx context.Context, prompt string, options ...interface{}) (string, error) {
	// Zwróć mock odpowiedź w zależności od promptu
	if contains(prompt, "sentiment") {
		return `{
			"sentiment": "positive",
			"intent": "inquiry",
			"technical_terms": ["klimatyzacja", "inverter"],
			"price_references": [8000.0],
			"extracted_info": {
				"room_size": "65",
				"budget": "8000",
				"special_requirements": "cicha praca"
			},
			"urgency": "medium"
		}`, nil
	}

	if contains(prompt, "requirements") {
		return `{
			"requirements": {
				"room_size": 65.0,
				"room_count": 2,
				"building_type": "apartment",
				"installation_type": "wall",
				"special_features": ["inverter", "wifi"],
				"energy_class": "A++",
				"noise_level": "low"
			},
			"budget": {
				"min_price": 6000.0,
				"max_price": 8000.0,
				"currency": "PLN",
				"flexible": true
			},
			"preferences": {
				"brand": "LG",
				"technology": ["inverter", "wifi"],
				"design": "modern",
				"installation_time": "flexible",
				"warranty": 5
			},
			"semantic_keywords": ["klimatyzacja", "cicha praca", "inverter"]
		}`, nil
	}

	// Domyślna odpowiedź dla wprowadzenia
	return "Szanowny Kliencie, dziękujemy za zainteresowanie naszymi systemami klimatyzacji. Przygotowaliśmy dla Państwa ofertę dopasowaną do wymagań.", nil
}

// contains - Helper function
func contains(text, substr string) bool {
	return len(text) > 0 && len(substr) > 0 &&
		text != substr &&
		(text == substr ||
			(len(text) > len(substr) &&
				(text[:len(substr)] == substr ||
					text[len(text)-len(substr):] == substr ||
					findSubstring(text, substr))))
}

func findSubstring(text, substr string) bool {
	for i := 0; i <= len(text)-len(substr); i++ {
		if text[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// TestOfferPDFGenerator - Test generatora PDF
func TestOfferPDFGenerator(t *testing.T) {
	generator := NewOfferPDFGenerator()

	// Stwórz przykładową ofertę
	offer := &GeneratedOffer{
		OfferID: "TEST-123",
		CustomerProfile: CustomerProfile{
			Name:  "Jan Kowalski",
			Email: "<EMAIL>",
		},
		Recommendations: []OfferRecommendation{
			{
				Equipment: LGEquipment{
					Name:        "LG Standard S12ET",
					ModelCode:   "S12ET",
					EnergyClass: "A++",
					Pricing: PricingInfo{
						TotalPrice: 5700.0,
					},
				},
				MatchScore: 0.9,
			},
		},
		PersonalizedIntro: "Test introduction",
		Summary: OfferSummary{
			FinalPrice: 5700.0,
		},
		GeneratedAt: time.Now(),
		ValidUntil:  time.Now().AddDate(0, 0, 30),
	}

	ctx := context.Background()
	pdfPath, err := generator.GeneratePDF(ctx, offer)
	if err != nil {
		t.Fatalf("Failed to generate PDF: %v", err)
	}

	if pdfPath == "" {
		t.Error("PDF path should not be empty")
	}

	t.Logf("✅ PDF generated successfully: %s", pdfPath)
}

// BenchmarkOfferGeneration - Benchmark generowania ofert
func BenchmarkOfferGeneration(b *testing.B) {
	generator, err := NewHVACOfferGenerator()
	if err != nil {
		b.Fatalf("Failed to create generator: %v", err)
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		customerID := "benchmark_customer"
		_, err := generator.GenerateOffer(ctx, customerID)
		if err != nil {
			b.Fatalf("Failed to generate offer: %v", err)
		}
	}
}
