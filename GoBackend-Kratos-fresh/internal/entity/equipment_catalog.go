package entity

import (
	"encoding/json"
	"time"
)

// StringArray represents a slice of strings for PostgreSQL array support
type StringArray []string

// 🏭 Equipment Catalog - Manufacturer Equipment Database
// Enhanced Equipment Registry with Automated Manufacturer Data

// EquipmentCatalog represents manufacturer equipment specifications
type EquipmentCatalog struct {
	ID             int64  `json:"id" gorm:"primaryKey;autoIncrement"`
	ManufacturerID int64  `json:"manufacturer_id" gorm:"not null;index"`
	ModelNumber    string `json:"model_number" gorm:"not null;size:100;index"`
	ProductName    string `json:"product_name" gorm:"not null;size:255"`
	ProductFamily  string `json:"product_family" gorm:"size:100;index"`
	Category       string `json:"category" gorm:"not null;size:100;index"` // air_conditioner, heat_pump, furnace, etc.
	SubCategory    string `json:"sub_category" gorm:"size:100"`

	// Technical Specifications
	Specifications  json.RawMessage `json:"specifications" gorm:"type:jsonb"`
	PerformanceData json.RawMessage `json:"performance_data" gorm:"type:jsonb"`
	DimensionsData  json.RawMessage `json:"dimensions_data" gorm:"type:jsonb"`
	ElectricalData  json.RawMessage `json:"electrical_data" gorm:"type:jsonb"`

	// Capacity & Efficiency
	CoolingCapacityBTU *int64   `json:"cooling_capacity_btu"`
	HeatingCapacityBTU *int64   `json:"heating_capacity_btu"`
	SEER               *float64 `json:"seer"` // Seasonal Energy Efficiency Ratio
	EER                *float64 `json:"eer"`  // Energy Efficiency Ratio
	HSPF               *float64 `json:"hspf"` // Heating Seasonal Performance Factor
	COP                *float64 `json:"cop"`  // Coefficient of Performance

	// Physical Properties
	Weight *float64 `json:"weight"` // in pounds
	Length *float64 `json:"length"` // in inches
	Width  *float64 `json:"width"`  // in inches
	Height *float64 `json:"height"` // in inches

	// Pricing & Availability
	MSRP             *float64   `json:"msrp"`
	DealerPrice      *float64   `json:"dealer_price"`
	IsAvailable      bool       `json:"is_available" gorm:"default:true"`
	DiscontinuedDate *time.Time `json:"discontinued_date"`

	// Certifications & Standards
	AHRINumber     string      `json:"ahri_number" gorm:"size:50;index"`
	EnergyStar     bool        `json:"energy_star" gorm:"default:false"`
	Certifications StringArray `json:"certifications" gorm:"type:text[]"`

	// Images & Documentation
	ImageURLs            StringArray `json:"image_urls" gorm:"type:text[]"`
	DocumentURLs         StringArray `json:"document_urls" gorm:"type:text[]"`
	TechnicalSheetURL    string      `json:"technical_sheet_url" gorm:"size:500"`
	InstallationGuideURL string      `json:"installation_guide_url" gorm:"size:500"`

	// Data Source & Quality
	DataSource       string     `json:"data_source" gorm:"size:100"` // crawl4ai, api, manual
	SourceURL        string     `json:"source_url" gorm:"size:500"`
	DataQualityScore float64    `json:"data_quality_score" gorm:"default:0.0"` // 0.0 to 1.0
	LastVerified     *time.Time `json:"last_verified"`

	// AI Analysis
	AIAnalysisData      json.RawMessage `json:"ai_analysis_data" gorm:"type:jsonb"`
	RecommendationScore float64         `json:"recommendation_score" gorm:"default:0.0"`

	// Metadata
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Manufacturer        *Manufacturer             `json:"manufacturer,omitempty" gorm:"foreignKey:ManufacturerID"`
	CatalogImages       []*CatalogImage           `json:"catalog_images,omitempty" gorm:"foreignKey:CatalogID"`
	CompatibleEquipment []*EquipmentCompatibility `json:"compatible_equipment,omitempty" gorm:"foreignKey:CatalogID"`
}

// Manufacturer represents HVAC equipment manufacturers
type Manufacturer struct {
	ID          int64  `json:"id" gorm:"primaryKey;autoIncrement"`
	Name        string `json:"name" gorm:"not null;size:100;uniqueIndex"`
	DisplayName string `json:"display_name" gorm:"size:100"`
	Website     string `json:"website" gorm:"size:255"`
	APIEndpoint string `json:"api_endpoint" gorm:"size:255"`
	APIKey      string `json:"api_key" gorm:"size:255"`
	LogoURL     string `json:"logo_url" gorm:"size:500"`

	// Crawling Configuration
	CrawlConfig    json.RawMessage `json:"crawl_config" gorm:"type:jsonb"`
	LastCrawled    *time.Time      `json:"last_crawled"`
	CrawlFrequency int             `json:"crawl_frequency" gorm:"default:24"` // hours

	// Contact Information
	SupportPhone     string `json:"support_phone" gorm:"size:50"`
	SupportEmail     string `json:"support_email" gorm:"size:100"`
	TechnicalSupport string `json:"technical_support" gorm:"size:100"`

	// Business Information
	IsActive bool   `json:"is_active" gorm:"default:true"`
	Country  string `json:"country" gorm:"size:50"`
	Region   string `json:"region" gorm:"size:50"`

	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// CatalogImage represents equipment images stored in MinIO
type CatalogImage struct {
	ID          int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	CatalogID   int64     `json:"catalog_id" gorm:"not null;index"`
	ImageType   string    `json:"image_type" gorm:"size:50"` // product, technical, installation, etc.
	FileName    string    `json:"file_name" gorm:"not null;size:255"`
	MinIOPath   string    `json:"minio_path" gorm:"not null;size:500"`
	MinIOBucket string    `json:"minio_bucket" gorm:"not null;size:100"`
	FileSize    int64     `json:"file_size"`
	MimeType    string    `json:"mime_type" gorm:"size:100"`
	Width       *int      `json:"width"`
	Height      *int      `json:"height"`
	AltText     string    `json:"alt_text" gorm:"size:255"`
	IsPrimary   bool      `json:"is_primary" gorm:"default:false"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
}

// EquipmentCompatibility represents compatibility between catalog items and installed equipment
type EquipmentCompatibility struct {
	ID                 int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	CatalogID          int64     `json:"catalog_id" gorm:"not null;index"`
	EquipmentID        int64     `json:"equipment_id" gorm:"not null;index"`
	CompatibilityType  string    `json:"compatibility_type" gorm:"size:50"`      // replacement, upgrade, accessory
	CompatibilityScore float64   `json:"compatibility_score" gorm:"default:0.0"` // 0.0 to 1.0
	Notes              string    `json:"notes" gorm:"type:text"`
	CreatedAt          time.Time `json:"created_at" gorm:"autoCreateTime"`

	// Relationships
	Catalog   *EquipmentCatalog `json:"catalog,omitempty" gorm:"foreignKey:CatalogID"`
	Equipment *Equipment        `json:"equipment,omitempty" gorm:"foreignKey:EquipmentID"`
}

// CrawlJob represents automated data collection jobs
type CrawlJob struct {
	ID             int64  `json:"id" gorm:"primaryKey;autoIncrement"`
	ManufacturerID int64  `json:"manufacturer_id" gorm:"not null;index"`
	JobType        string `json:"job_type" gorm:"not null;size:50"` // full_catalog, incremental, images, specs
	Status         string `json:"status" gorm:"not null;size:50"`   // pending, running, completed, failed
	Priority       int    `json:"priority" gorm:"default:5"`        // 1-10, higher is more priority

	// Job Configuration
	CrawlConfig json.RawMessage `json:"crawl_config" gorm:"type:jsonb"`
	TargetURLs  StringArray     `json:"target_urls" gorm:"type:text[]"`

	// Progress Tracking
	TotalItems      int `json:"total_items" gorm:"default:0"`
	ProcessedItems  int `json:"processed_items" gorm:"default:0"`
	SuccessfulItems int `json:"successful_items" gorm:"default:0"`
	FailedItems     int `json:"failed_items" gorm:"default:0"`

	// Results
	ResultData json.RawMessage `json:"result_data" gorm:"type:jsonb"`
	ErrorLog   string          `json:"error_log" gorm:"type:text"`

	// Timing
	ScheduledAt *time.Time `json:"scheduled_at"`
	StartedAt   *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
	Duration    *int64     `json:"duration"` // seconds

	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Manufacturer *Manufacturer `json:"manufacturer,omitempty" gorm:"foreignKey:ManufacturerID"`
}

// Table names
func (EquipmentCatalog) TableName() string {
	return "equipment_catalog"
}

func (Manufacturer) TableName() string {
	return "manufacturers"
}

func (CatalogImage) TableName() string {
	return "catalog_images"
}

func (EquipmentCompatibility) TableName() string {
	return "equipment_compatibility"
}

func (CrawlJob) TableName() string {
	return "crawl_jobs"
}

// Helper methods for EquipmentCatalog
func (ec *EquipmentCatalog) GetSpecification(key string) interface{} {
	if ec.Specifications == nil {
		return nil
	}

	var specs map[string]interface{}
	if err := json.Unmarshal(ec.Specifications, &specs); err != nil {
		return nil
	}

	return specs[key]
}

func (ec *EquipmentCatalog) SetSpecification(key string, value interface{}) error {
	var specs map[string]interface{}
	if ec.Specifications != nil {
		if err := json.Unmarshal(ec.Specifications, &specs); err != nil {
			return err
		}
	} else {
		specs = make(map[string]interface{})
	}

	specs[key] = value

	data, err := json.Marshal(specs)
	if err != nil {
		return err
	}

	ec.Specifications = data
	return nil
}

// Helper methods for Manufacturer
func (m *Manufacturer) ShouldCrawl() bool {
	if !m.IsActive {
		return false
	}

	if m.LastCrawled == nil {
		return true
	}

	nextCrawl := m.LastCrawled.Add(time.Duration(m.CrawlFrequency) * time.Hour)
	return time.Now().After(nextCrawl)
}

// Helper methods for CrawlJob
func (cj *CrawlJob) CalculateProgress() float64 {
	if cj.TotalItems == 0 {
		return 0.0
	}
	return float64(cj.ProcessedItems) / float64(cj.TotalItems) * 100.0
}

func (cj *CrawlJob) CalculateSuccessRate() float64 {
	if cj.ProcessedItems == 0 {
		return 0.0
	}
	return float64(cj.SuccessfulItems) / float64(cj.ProcessedItems) * 100.0
}
