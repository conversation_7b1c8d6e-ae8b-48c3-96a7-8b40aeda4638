package email

import (
	"time"

	"gobackend-hvac-kratos/internal/ai"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/philippgille/chromem-go"
	"github.com/tmc/langchaingo/llms/ollama"
)

// EmailAnalysisConfig holds configuration for the email analysis service
type EmailAnalysisConfig struct {
	OllamaURL         string
	MaxAttachmentSize int64
	SupportedFormats  []string
}

// EmailAnalysisService is the main service for email analysis
type EmailAnalysisService struct {
	log      log.Logger
	vectorDB *chromem.DB
	llm      *ollama.LLM
	gemma3   *ai.Gemma3Service
	config   *EmailAnalysisConfig
}

// EmailAnalysisResult holds the result of an email analysis
type EmailAnalysisResult struct {
	EmailID         string
	Subject         string
	From            string
	To              []string
	Timestamp       time.Time
	BodyAnalysis    *TextAnalysis
	Attachments     []*AttachmentAnalysis
	AttachmentCount int
	Sentiment       string
	SentimentScore  float64
	Priority        string
	Category        string
	HVACRelevance   bool
	ActionItems     []string
}

// TextAnalysis holds the analysis results for text content
type TextAnalysis struct {
	Content    string   `json:"content"`
	WordCount  int      `json:"word_count"`
	Language   string   `json:"language"`
	KeyPhrases []string `json:"key_phrases"`
	Summary    string   `json:"summary"`
	Entities   []string `json:"entities"`
}

// AttachmentAnalysis holds the analysis results for attachments
type AttachmentAnalysis struct {
	Filename    string        `json:"filename"`
	ContentType string        `json:"content_type"`
	Size        int64         `json:"size"`
	Format      string        `json:"format"`
	TextContent string        `json:"text_content,omitempty"`
	Analysis    *TextAnalysis `json:"analysis,omitempty"`
	ExcelData   *ExcelData    `json:"excel_data,omitempty"`
	IsProcessed bool          `json:"is_processed"`
	Error       string        `json:"error,omitempty"`
}

// ExcelData holds the data from Excel files
type ExcelData struct {
	Sheets   []string            `json:"sheets"`
	RowCount int                 `json:"row_count"`
	ColCount int                 `json:"col_count"`
	Headers  []string            `json:"headers"`
	Summary  string              `json:"summary"`
	Data     []map[string]string `json:"data,omitempty"`
}
